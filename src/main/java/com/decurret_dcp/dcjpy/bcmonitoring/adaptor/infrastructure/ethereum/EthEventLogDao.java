package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum;

import static com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser.contractAddresses;
import static com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter.TUPLE_ARRAY_TYPE;
import static com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiTypeConverter.TUPLE_TYPE;

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser;
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.reactivex.disposables.Disposable;
import java.io.IOException;
import java.math.BigInteger;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import org.web3j.abi.EventValues;
import org.web3j.abi.datatypes.DynamicArray;
import org.web3j.abi.datatypes.DynamicStruct;
import org.web3j.abi.datatypes.StaticStruct;
import org.web3j.abi.datatypes.Type;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameter;
import org.web3j.protocol.core.DefaultBlockParameterNumber;
import org.web3j.protocol.core.methods.request.EthFilter;
import org.web3j.protocol.core.methods.response.*;
import org.web3j.tx.Contract;

@Component
@Log4j2
public class EthEventLogDao {
  private final BcmonitoringConfigurationProperties properties;
  private final Web3jConfig web3jConfig;
  private final AbiParser abiParser;
  private final ObjectMapper objectMapper;
  private Disposable subscription;

  private static final int BATCH_SIZE = 5000;

  /**
   * Constructor for EthEventLogDao.
   *
   * @param properties The configuration properties.
   * @param web3jConfig The Web3j configuration.
   * @param abiParser The ABI parser.
   * @param objectMapper The object mapper.
   */
  public EthEventLogDao(
      BcmonitoringConfigurationProperties properties,
      Web3jConfig web3jConfig,
      AbiParser abiParser,
      ObjectMapper objectMapper) {
    this.properties = properties;
    this.web3jConfig = web3jConfig;
    this.abiParser = abiParser;
    this.objectMapper = objectMapper;
  }

  /**
   * Subscribes to all blocks and processes transactions.
   *
   * @return A BlockingQueue of Transaction objects.
   */
  public BlockingQueue<Transaction> subscribeAll() {
    BlockingQueue<Transaction> transactions = new LinkedBlockingQueue<>(Integer.MAX_VALUE);

    // Check if the difference is valid
    int allowableDiff;
    try {
      allowableDiff =
          Integer.parseInt(properties.getSubscription().getAllowableBlockTimestampDiffSec());
    } catch (NumberFormatException e) {
      log.error("Failed to parse allowable timestamp difference", e);
      return null;
    }

    try {
      // Create a new Web3j instance for this subscription
      Web3j web3j = web3jConfig.getWeb3j();
      // Create a new Web3j instance for RPC API calls
      Web3j web3jCaller = web3jConfig.getWeb3jCaller();
      // Subscribe to new blocks
      this.subscription =
          web3j
              .logsNotifications(contractAddresses, Collections.emptyList())
              .subscribe(
                  logNotification -> {
                    try {
                      EthBlock.Block block =
                          web3jCaller
                              .ethGetBlockByNumber(
                                  () -> logNotification.getParams().getResult().getBlockNumber(),
                                  true)
                              .send()
                              .getBlock();
                      BigInteger blockNumber = block.getNumber();
                      if (isDelayed(block, allowableDiff)) {
                        log.warn(
                            "Block {} is delayed by more than {} seconds",
                            blockNumber,
                            allowableDiff);
                      }

                      // Check if block has no transactions
                      if (block.getTransactions() == null || block.getTransactions().isEmpty()) {
                        log.info("Block {} has no transactions", blockNumber);
                        return;
                      }

                      // Process block transactions and events
                      List<Event> events = null;
                      try {
                        events = convBlock2EventEntities(block);
                      } catch (Exception e) {
                        throw new RuntimeException(e);
                      }

                      // Always create transaction, even if events list is empty
                      if (events != null) {
                        if (!events.isEmpty()) {
                          log.info("detect block includes events");
                        }

                        BlockHeight blockHeight =
                            BlockHeight.builder().blockNumber(blockNumber.longValue()).build();
                        Transaction transaction =
                            Transaction.builder().events(events).blockHeight(blockHeight).build();
                        try {
                          transactions.put(transaction);
                        } catch (InterruptedException e) {
                          log.error("Failed to put transaction", e);
                          throw new RuntimeException(e);
                        }
                      }
                    } catch (Exception throwable) {
                      log.error("Error processing block", throwable);
                      // Put error transaction to signal error condition
                      try {
                        transactions.put(
                            Transaction.builder()
                                .blockHeight(BlockHeight.builder().blockNumber(-1).build())
                                .build());
                      } catch (InterruptedException e) {
                        log.error("Failed to put error transaction", e);
                        Thread.currentThread().interrupt();
                      }
                    }
                  },
                  error -> {
                    log.error("Subscription error", error);
                    unsubscribe();
                    web3jConfig.shutdownWeb3j();
                    transactions.put(
                        Transaction.builder()
                            .blockHeight(BlockHeight.builder().blockNumber(-1).build())
                            .build());
                  },
                  () -> log.info("Subscription completed"));
      return transactions;
    } catch (Exception e) {
      log.error("Failed to create Web3j subscription", e);
      throw e;
    }
  }

  /**
   * Checks if the block is delayed based on the allowable difference in seconds.
   *
   * @param block The block to check.
   * @param allowableDiffSeconds The allowable difference in seconds.
   * @return true if the block is delayed, false otherwise.
   */
  private boolean isDelayed(EthBlock.Block block, int allowableDiffSeconds) {
    long blockTimestamp = block.getTimestamp().longValue();
    long currentTime = Instant.now().getEpochSecond();
    long diff = currentTime - blockTimestamp;

    return diff > allowableDiffSeconds;
  }

  /**
   * Converts a block to a collection of event entities.
   *
   * @param block Ethereum block
   * @return Collection of events found in the block
   * @throws IOException If there is an error communicating with the Ethereum node
   * @throws ExecutionException If there is an error executing the transaction
   * @throws InterruptedException If the operation is interrupted
   */
  public List<Event> convBlock2EventEntities(EthBlock.Block block) throws Exception {
    List<Event> events = new ArrayList<>();

    try {
      // Create a new Web3j instance for RPC API calls
      Web3j web3jCaller = web3jConfig.getWeb3jCaller();

      for (EthBlock.TransactionResult txResult : block.getTransactions()) {
        // Return error if transaction is null
        if (txResult.get() == null) {
          throw new RuntimeException("Transaction is null");
        }

        try {
          EthBlock.TransactionObject transaction = (EthBlock.TransactionObject) txResult.get();
          String transactionHash = transaction.getHash();

          EthGetTransactionReceipt receiptResponse =
              web3jCaller.ethGetTransactionReceipt(transactionHash).send();

          TransactionReceipt receipt = receiptResponse.getTransactionReceipt().orElse(null);
          if (receipt == null) {
            throw new RuntimeException("Transaction receipt is null");
          }

          for (Log logEvent : receipt.getLogs()) {
            try {
              log.info("Event found tx_hash={}", logEvent.getTransactionHash());
              Event event =
                  convertEthLogToEventEntity(logEvent)
                      .withBlockTimestamp(block.getTimestamp().longValue());
              log.info("Event parsed tx_hash={}, name={}", event.transactionHash, event.name);
              events.add(event);
            } catch (Exception e) {
              log.error("Error processing log for transaction {}", logEvent.getTransactionHash());
              throw e;
            }
          }
        } catch (Exception e) {
          log.error("Error processing transaction", e.getMessage());
          throw e;
        }
      }
    } catch (Exception e) {
      log.error("Error converting block to events: {}", e.getMessage());
      throw e;
    }

    return events;
  }

  /**
   * Converts an Ethereum log to an Event entity.
   *
   * @param ethLog The Ethereum log to convert
   * @return Converted Event entity
   * @throws Exception If conversion fails
   */
  public Event convertEthLogToEventEntity(Log ethLog) throws Exception {
    try {
      // Get ABI event definition for the log
      org.web3j.abi.datatypes.Event abiEvent = abiParser.getABIEventByLog(ethLog);
      if (abiEvent == null) {
        log.info("Event definition not found in ABI");
        throw new Exception("Event definition not found in ABI");
      }

      // Get contract ABI event to access parameter names
      var contractAbiEvent = abiParser.getContractAbiEventByLog(ethLog);

      // Extract event parameters using Web3j's utilities
      EventValues eventValues = Contract.staticExtractEventParameters(abiEvent, ethLog);
      if (eventValues == null) {
        log.info("No event values found for log: {}", ethLog);
        throw new Exception("No event values found for log");
      }

      List<Type> indexedParameters = eventValues.getIndexedValues();
      List<Type> nonIndexedParameters = eventValues.getNonIndexedValues();
      Map<Boolean, List<AbiParser.AbiEventInput>> groupedInputs =
          contractAbiEvent.getInputs().stream()
              .collect(
                  Collectors.groupingBy(
                      AbiParser.AbiEventInput::isIndexed, LinkedHashMap::new, Collectors.toList()));

      Map<String, Object> indexedValues =
          decodeEventParameters(
              indexedParameters, groupedInputs.getOrDefault(Boolean.TRUE, Collections.emptyList()));
      Map<String, Object> nonIndexedValues =
          decodeEventParameters(
              nonIndexedParameters,
              groupedInputs.getOrDefault(Boolean.FALSE, Collections.emptyList()));

      String indexedJson = objectMapper.writeValueAsString(indexedValues);
      String nonIndexedJson = objectMapper.writeValueAsString(nonIndexedValues);

      // Serialize log to JSON
      String logJson = objectMapper.writeValueAsString(ethLog);

      // Create and return new Event entity
      return Event.builder()
          .name(abiEvent.getName())
          .transactionHash(ethLog.getTransactionHash())
          .logIndex((int) ethLog.getLogIndex().longValue())
          .indexedValues(indexedJson)
          .nonIndexedValues(nonIndexedJson)
          .log(logJson)
          .build();
    } catch (Exception e) {
      log.error("Error converting log to event entity", e);
      return null;
    }
  }

  /**
   * Decodes a list of event parameter values based on their corresponding ABI event input
   * definitions.
   *
   * <p>This method maps the decoded values to their respective parameter names as defined in the
   * ABI, supporting complex types such as tuples, tuple arrays, and dynamic arrays.
   *
   * @param values the list of decoded {@link Type} values from the event log
   * @param inputs the list of expected ABI event input definitions
   * @return a map of parameter names to their decoded values, preserving the order of inputs
   */
  private Map<String, Object> decodeEventParameters(
      List<Type> values, List<AbiParser.AbiEventInput> inputs) {
    Map<String, Object> result = new LinkedHashMap<>();
    if (values == null || values.isEmpty() || inputs == null || inputs.isEmpty()) {
      return result;
    }
    int index = 0;
    for (var input : inputs) {
      if (index >= values.size()) break;

      String name = input.getName();
      Object value = values.get(index).getValue();

      if (TUPLE_TYPE.equals(input.getType()) && value instanceof List<?> list) {
        // Handle tuple type: tuple
        result.put(name, decodeTuple(list, input.getComponents()));
      } else if (TUPLE_ARRAY_TYPE.equals(input.getType()) && value instanceof List<?> list) {
        // Handle tuple array type: tuple[]
        result.put(name, decodeTupleArray(list, input.getComponents()));
      } else if (input.getType() != null
          && input.getType().endsWith("[]")
          && values.get(index) instanceof DynamicArray<?>) {
        // Handle dynamic array type: uint8[], bytes32[], etc.
        result.put(name, decodeDynamicArray(value));
      } else {
        // Handle basic types: unit8, bytes32, etc.
        result.put(name, value);
      }

      index++;
    }
    return result;
  }

  /**
   * Decode Tuple
   *
   * @param list a list of Type objects
   * @param components a list of AbiEventInput objects
   * @return a map of strings to objects
   */
  private Map<String, Object> decodeTuple(List<?> list, List<AbiParser.AbiEventInput> components) {
    Map<String, Object> tupleResult = new LinkedHashMap<>();
    if (list == null || list.isEmpty() || components == null || components.isEmpty()) {
      return tupleResult;
    }
    for (int i = 0; i < components.size(); i++) {
      String compName = components.get(i).getName();
      Object compValue = ((Type<?>) list.get(i)).getValue();

      if (TUPLE_TYPE.equals(components.get(i).getType())
          && compValue instanceof List<?> nestedList) {
        tupleResult.put(compName, decodeTuple(nestedList, components.get(i).getComponents()));
      } else {
        if (list.get(i) instanceof DynamicArray<?>) {
          tupleResult.put(compName, decodeDynamicArray(compValue));
        } else {
          tupleResult.put(compName, compValue);
        }
      }
    }
    return tupleResult;
  }

  /**
   * Decode Tuple Array
   *
   * @param list a list of Type objects representing an array of tuples
   * @param components a list of AbiEventInput objects defining the tuple structure
   * @return a list of maps, each representing a decoded tuple
   */
  private List<Map<String, Object>> decodeTupleArray(
      List<?> list, List<AbiParser.AbiEventInput> components) {
    List<Map<String, Object>> tupleArrayResult = new ArrayList<>();
    if (list == null || list.isEmpty() || components == null || components.isEmpty()) {
      return tupleArrayResult;
    }

    // For tuple arrays, the list should contain struct objects (DynamicStruct or StaticStruct)
    for (Object item : list) {
      if (item instanceof DynamicStruct struct) {
        tupleArrayResult.add(decodeTuple(struct.getValue(), components));
      } else if (item instanceof StaticStruct struct) {
        tupleArrayResult.add(decodeTuple(struct.getValue(), components));
      }
    }

    return tupleArrayResult;
  }

  /**
   * Decode Dynamic Array
   *
   * @param value an ArrayList of Type objects
   * @return a list of objects
   */
  private List<Object> decodeDynamicArray(Object value) {
    ArrayList<Type> dynamicArray = (ArrayList<Type>) value;
    return dynamicArray.stream().map(Type::getValue).toList();
  }

  /**
   * Retrieves the block timestamp for a given block number.
   *
   * @param blockNumber The block number to retrieve the timestamp for.
   * @return The block timestamp in seconds since epoch.
   * @throws IOException If there is an error communicating with the Ethereum node
   */
  private long getBlockTimestamp(BigInteger blockNumber) throws IOException {
    // Create a new Web3j instance for this operation
    Web3j web3j = web3jConfig.getWeb3j();

    try {
      return web3j
          .ethGetBlockByNumber(DefaultBlockParameter.valueOf(blockNumber), false)
          .send()
          .getBlock()
          .getTimestamp()
          .longValue();
    } finally {
      // Shutdown the Web3j instance to free resources
      web3j.shutdown();
    }
  }

  /**
   * Get filtered logs from a specific block height
   *
   * @param blockHeight Block number to start from
   * @return Queue of transactions containing events
   */
  public List<Transaction> getPendingTransactions(long blockHeight) {
    return getPendingTransactions(blockHeight, false);
  }

  /**
   * Get filtered logs from a specific block height with an option to force an error in the outer
   * catch block This method is primarily used for testing the error handling in the outer catch
   * block
   *
   * @param blockHeight Block number to start from
   * @param forceOuterError Whether to force an error in the outer catch block (for testing)
   * @return Queue of transactions containing events
   */
  public List<Transaction> getPendingTransactions(long blockHeight, boolean forceOuterError) {
    try {
      Web3j web3j = web3jConfig.getWeb3j();
      BigInteger currentBlockNumber = getCurrentBlockNumber(web3j);
      BigInteger fromBlock = BigInteger.valueOf(blockHeight);

      return processBatchedTransactions(web3j, fromBlock, currentBlockNumber, forceOuterError);

    } catch (Exception e) {
      log.error("Error getting filtered logs", e);
      throw new RuntimeException("Error getting filtered logs", e);
    }
  }

  /**
   * Get current block number from Ethereum node
   *
   * @param web3j Web3j instance
   * @return Current block number
   */
  private BigInteger getCurrentBlockNumber(Web3j web3j) throws IOException {
    return web3j.ethBlockNumber().send().getBlockNumber();
  }

  /**
   * Process batched transactions
   *
   * @param web3j Web3j instance
   * @param fromBlock Start block number
   * @param currentBlockNumber Current block number
   * @param forceOuterError Whether to force an error in the outer catch block (for testing)
   * @return List of processed transactions
   * @throws IOException If there is an error communicating with the Ethereum node
   */
  private List<Transaction> processBatchedTransactions(
      Web3j web3j, BigInteger fromBlock, BigInteger currentBlockNumber, boolean forceOuterError)
      throws IOException {

    List<Transaction> allTransactions = new ArrayList<>();
    Map<BigInteger, BigInteger> blockTimestamps = new HashMap<>();
    
    while (fromBlock.compareTo(currentBlockNumber) <= 0) {
      BigInteger toBlock = calculateToBlock(fromBlock, currentBlockNumber);

      log.info("Getting transactions for batch [{} - {}] of total range [{} - {}]",
              fromBlock, toBlock, fromBlock, currentBlockNumber);

      List<EthLog.LogResult> batchLogs = getEthLogs(web3j, fromBlock, toBlock);

      if (!batchLogs.isEmpty()) {
        fetchBlockTimestamps(web3j, batchLogs, blockTimestamps);

        if (forceOuterError) {
          throw new RuntimeException("Forced error in outer catch block for testing");
        }

        List<Transaction> batchTransactions = processBatchLogs(batchLogs, blockTimestamps);
        allTransactions.addAll(batchTransactions);
      }

      fromBlock = toBlock.add(BigInteger.ONE);
    }

    log.info(
        "Completed getting {} total transactions from block {} to {}",
        allTransactions.size(),
        fromBlock,
        currentBlockNumber);
    return allTransactions;
  }

  /**
   * Calculate the to block number for a batch of transactions
   *
   * @param fromBlock From block number
   * @param currentBlockNumber Current block number
   * @return To block number
   */
  private BigInteger calculateToBlock(
      BigInteger fromBlock, BigInteger currentBlockNumber) {
    BigInteger toBlock = fromBlock.add(BigInteger.valueOf(BATCH_SIZE - 1));
    return toBlock.compareTo(currentBlockNumber) > 0 ? currentBlockNumber : toBlock;
  }

  /**
   * Get batch logs for a range of block numbers
   *
   * @param web3j Web3j instance
   * @param fromBlock From block number
   * @param toBlock To block number
   * @return List of log results
   * @throws IOException If there is an error communicating with the Ethereum node
   */
  private List<EthLog.LogResult> getEthLogs(Web3j web3j, BigInteger fromBlock, BigInteger toBlock)
      throws IOException {
    EthFilter filter =
        new EthFilter(
            DefaultBlockParameter.valueOf(fromBlock),
            DefaultBlockParameter.valueOf(toBlock),
            contractAddresses);

    List<EthLog.LogResult> batchLogs = web3j.ethGetLogs(filter).send().getLogs();
    log.info("Retrieved {} logs from block {} to block {}", batchLogs.size(), fromBlock, toBlock);
    return batchLogs;
  }

  /**
   * Fetch block timestamps for a batch of logs
   *
   * @param web3j Web3j instance
   * @param batchLogs Batch logs
   * @param blockTimestamps Map of block number to timestamp
   * @throws IOException If there is an error communicating with the Ethereum node
   */
  private void fetchBlockTimestamps(
      Web3j web3j, List<EthLog.LogResult> batchLogs, Map<BigInteger, BigInteger> blockTimestamps)
      throws IOException {

    Set<BigInteger> batchBlockNumbers =
        batchLogs.stream()
            .map(result -> ((Log) result.get()).getBlockNumber())
            .collect(Collectors.toSet());

    for (BigInteger blockNumber : batchBlockNumbers) {
      if (!blockTimestamps.containsKey(blockNumber)) {
        EthBlock block =
            web3j.ethGetBlockByNumber(new DefaultBlockParameterNumber(blockNumber), false).send();
        blockTimestamps.put(blockNumber, block.getBlock().getTimestamp());
      }
    }
  }

  /**
   * Process batch logs
   *
   * @param batchLogs Batch logs
   * @param blockTimestamps Map of block number to timestamp
   * @return List of transaction entities
   */
  private List<Transaction> processBatchLogs(
      List<EthLog.LogResult> batchLogs, Map<BigInteger, BigInteger> blockTimestamps) {

    return batchLogs.stream()
        .map(logResult -> processIndividualLog(logResult, blockTimestamps))
        .filter(Objects::nonNull)
        .toList();
  }

  /**
   * Process individual log result
   *
   * @param logResult Log result
   * @param blockTimestamps Map of block number to timestamp
   * @return Transaction entity
   */
  private Transaction processIndividualLog(
      EthLog.LogResult logResult, Map<BigInteger, BigInteger> blockTimestamps) {
    try {
      Log ethLog = (Log) logResult.get();
      log.info("Event found tx_hash={}", ethLog.getTransactionHash());

      Event event =
          convertEthLogToEventEntity(ethLog)
              .withBlockTimestamp(blockTimestamps.get(ethLog.getBlockNumber()).longValue());
      log.info("Event parsed tx_hash={}, name={}", event.transactionHash, event.name);

      BlockHeight height =
          BlockHeight.builder().blockNumber(ethLog.getBlockNumber().longValue()).build();

      return Transaction.builder()
          .events(Collections.singletonList(event))
          .blockHeight(height)
          .build();
    } catch (Exception e) {
      log.error("Error processing individual log", e);
      return null;
    }
  }

  public void unsubscribe() {
    if (subscription != null) {
      subscription.dispose();
    }
  }
}
